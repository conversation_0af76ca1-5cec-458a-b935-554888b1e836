// app/page.tsx (Next.js 15 with App Router)
"use client";

import { useState } from "react";
import {
  Heart,
  Share2,
  Facebook,
  Twitter,
  Linkedin,
  Copy,
  Bookmark,
  Eye,
  Clock,
  Volume2,
  Printer,
  Minus,
  Plus,
  User,
  Tag,
} from "lucide-react";

export default function BlogPost() {
  const [likes, setLikes] = useState(120);
  const [liked, setLiked] = useState(false);

  const toggleLike = () => {
    setLiked(!liked);
    setLikes((prev) => prev + (liked ? -1 : 1));
  };

  return (
    <div className="relative min-h-screen bg-gray-50 pt-40">
      {/* 📊 Progress Bar */}
      <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 z-50">
        <div className="h-full w-1/3 bg-blue-500" /> {/* static width for UI */}
      </div>

      <main className="max-w-5xl mx-auto px-4 md:px-8 py-10">
        {/* 📋 Table of Contents */}
        <aside className="hidden lg:block fixed right-8 top-28 w-64">
          <h3 className="font-semibold mb-3">Table of Contents</h3>
          <ul className="space-y-2 text-sm border-l border-gray-300 pl-3">
            <li className="text-blue-600 font-medium">Introduction</li>
            <li>Features</li>
            <li>Examples</li>
            <li>Conclusion</li>
          </ul>
        </aside>

        {/* Blog Content */}
        <article className="prose prose-lg max-w-none">
          <h1 className="text-4xl font-bold mb-4">Beautiful Blog UI in Next.js 15</h1>

          {/* 📈 Stats */}
          <div className="flex flex-wrap gap-6 text-gray-600 text-sm mb-8">
            <span className="flex items-center gap-1"><Eye className="w-4 h-4" /> 4.5k views</span>
            <span className="flex items-center gap-1"><Heart className="w-4 h-4" /> {likes} likes</span>
            <span className="flex items-center gap-1"><Clock className="w-4 h-4" /> 5 min read</span>
          </div>

          <p>
            lorem10
          </p>
          <h2>Features</h2>
          <p>Here are the main features...</p>
          <h2>Examples</h2>
          <p>Practical examples below...</p>
          <h2>Conclusion</h2>
          <p>Final thoughts...</p>
        </article>

        {/* 🛠️ Reading Tools */}
        <div className="flex gap-4 my-8 text-gray-700">
          <button className="flex items-center gap-1 hover:text-blue-600"><Minus className="w-4 h-4" />A</button>
          <button className="flex items-center gap-1 hover:text-blue-600"><Plus className="w-4 h-4" />A</button>
          <button className="flex items-center gap-1 hover:text-blue-600"><Printer className="w-4 h-4" />Print</button>
          <button className="flex items-center gap-1 hover:text-blue-600"><Volume2 className="w-4 h-4" />Read</button>
        </div>

        {/* ❤️ Like + 📱 Share */}
        <div className="flex items-center justify-between py-6 border-t border-b mt-10">
          <button
            onClick={toggleLike}
            className={`flex items-center gap-2 text-lg transition ${
              liked ? "text-red-500" : "text-gray-600"
            }`}
          >
            <Heart className={`w-6 h-6 ${liked ? "fill-red-500" : ""}`} />
            {likes}
          </button>

          <div className="flex gap-3">
            <button className="p-2 rounded-full hover:bg-gray-200"><Twitter className="w-4 h-4" /></button>
            <button className="p-2 rounded-full hover:bg-gray-200"><Facebook className="w-4 h-4" /></button>
            <button className="p-2 rounded-full hover:bg-gray-200"><Linkedin className="w-4 h-4" /></button>
            <button className="p-2 rounded-full hover:bg-gray-200"><Share2 className="w-4 h-4" /></button>
            <button className="p-2 rounded-full hover:bg-gray-200"><Copy className="w-4 h-4" /></button>
            <button className="p-2 rounded-full hover:bg-gray-200"><Bookmark className="w-4 h-4" /></button>
          </div>
        </div>

        {/* 🔗 Related Posts */}
        <section className="mt-12">
          <h3 className="text-xl font-semibold mb-4">Related Posts</h3>
          <div className="grid md:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white p-4 rounded-lg shadow hover:shadow-md transition">
                <h4 className="font-semibold">Post Title {i}</h4>
                <p className="text-sm text-gray-500 mt-2">Short description of post {i}...</p>
              </div>
            ))}
          </div>
        </section>

        {/* 🏷️ Tags */}
        <div className="flex flex-wrap gap-2 mt-8">
          {["Next.js", "Tailwind", "Design", "UI"].map((tag) => (
            <span
              key={tag}
              className="flex items-center gap-1 text-sm bg-gray-200 px-3 py-1 rounded-full"
            >
              <Tag className="w-3 h-3" /> {tag}
            </span>
          ))}
        </div>

        {/* 👤 Author Bio */}
        <section className="mt-12 flex gap-4 items-center bg-white p-6 rounded-lg shadow">
          <div className="w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center">
            <User className="w-8 h-8 text-gray-600" />
          </div>
          <div>
            <h4 className="font-semibold text-lg">Jane Doe</h4>
            <p className="text-gray-600 text-sm">Tech Blogger • 5+ years writing about web dev</p>
            <div className="flex gap-3 mt-2 text-gray-600">
              <Twitter className="w-4 h-4 hover:text-blue-500 cursor-pointer" />
              <Linkedin className="w-4 h-4 hover:text-blue-700 cursor-pointer" />
            </div>
          </div>
        </section>

        {/* 💬 Comments */}
        <section className="mt-12">
          <h3 className="text-xl font-semibold mb-4">Comments</h3>
          <div className="space-y-6">
            {[1, 2].map((i) => (
              <div key={i} className="p-4 bg-gray-100 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-8 h-8 bg-gray-300 rounded-full" />
                  <span className="font-medium">User {i}</span>
                </div>
                <p className="text-sm text-gray-700">This is a sample comment #{i}.</p>
                <div className="flex gap-4 mt-2 text-xs text-gray-500">
                  <button className="hover:underline">Reply</button>
                  <button className="hover:underline flex items-center gap-1">
                    <Heart className="w-3 h-3" /> Like
                  </button>
                </div>
              </div>
            ))}
          </div>
        </section>
      </main>
    </div>
  );
}
