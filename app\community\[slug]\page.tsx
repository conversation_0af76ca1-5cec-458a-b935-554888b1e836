"use client";

import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  Heart,
  Share2,
  Clock,
  Volume2,
  Printer,
  Minus,
  Plus,
  BookOpen,
  ArrowUp,
  Tag,
  TrendingUp,
} from "lucide-react";

// Mock data - replace with your actual data fetching
const mockBlogPost = {
  id: "1",
  title: "The Future of AI in Business: A Comprehensive Guide to Digital Transformation",
  slug: "future-ai-business-digital-transformation",
  content: `
    <h2>Introduction to AI in Modern Business</h2>
    <p>Artificial Intelligence is revolutionizing the way businesses operate, from automating routine tasks to providing deep insights through data analysis. In this comprehensive guide, we'll explore how AI is reshaping industries and what it means for the future of work.</p>

    <h2>Key Benefits of AI Implementation</h2>
    <p>The implementation of AI technologies brings numerous advantages to businesses of all sizes. These benefits include increased efficiency, reduced operational costs, improved customer experiences, and enhanced decision-making capabilities.</p>

    <h3>Automation and Efficiency</h3>
    <p>AI-powered automation can handle repetitive tasks, freeing up human resources for more strategic work. This leads to significant productivity gains and allows employees to focus on creative and high-value activities.</p>

    <h3>Data-Driven Insights</h3>
    <p>Machine learning algorithms can analyze vast amounts of data to uncover patterns and trends that would be impossible for humans to detect manually. This enables businesses to make more informed decisions based on concrete evidence rather than intuition.</p>

    <h2>Real-World Applications</h2>
    <p>From chatbots providing 24/7 customer support to predictive analytics optimizing supply chains, AI applications are diverse and growing rapidly. Companies across industries are finding innovative ways to leverage AI for competitive advantage.</p>

    <h2>Challenges and Considerations</h2>
    <p>While AI offers tremendous opportunities, it also presents challenges including data privacy concerns, the need for skilled personnel, and potential job displacement. Organizations must carefully plan their AI adoption strategy to maximize benefits while mitigating risks.</p>

    <h2>Future Outlook</h2>
    <p>As AI technology continues to evolve, we can expect even more sophisticated applications and broader adoption across industries. The businesses that start their AI journey today will be best positioned to thrive in the digital future.</p>
  `,
  excerpt: "Discover how artificial intelligence is transforming modern business operations and learn practical strategies for successful AI implementation in your organization.",
  publishedAt: "2024-01-15",
  readingTime: 8,
  views: 12547,
  likes: 342,
  bookmarks: 89,
  author: {
    id: "1",
    name: "Dr. Sarah Chen",
    bio: "AI Research Director with 15+ years of experience in machine learning and business transformation. Former Google AI researcher, now helping companies navigate their digital transformation journey.",
    avatar: "https://i.pravatar.cc/150?u=sarah-chen",
    role: "AI Research Director",
    company: "Kodaze",
    location: "San Francisco, CA",
    experience: "15+ years",
    articles: 47,
    followers: 12500,
    verified: true,
    social: {
      twitter: "https://twitter.com/sarahchen",
      linkedin: "https://linkedin.com/in/sarahchen",
      website: "https://sarahchen.ai"
    },
    expertise: ["Machine Learning", "Business Strategy", "Digital Transformation", "AI Ethics"]
  },
  categories: ["AI & Technology", "Business Strategy", "Digital Transformation"],
  tags: ["Artificial Intelligence", "Machine Learning", "Business Automation", "Digital Innovation", "Future of Work"],
  featuredImage: "https://picsum.photos/seed/ai-business/1200/600",
  tableOfContents: [
    { id: "introduction", title: "Introduction to AI in Modern Business", level: 2 },
    { id: "benefits", title: "Key Benefits of AI Implementation", level: 2 },
    { id: "automation", title: "Automation and Efficiency", level: 3 },
    { id: "insights", title: "Data-Driven Insights", level: 3 },
    { id: "applications", title: "Real-World Applications", level: 2 },
    { id: "challenges", title: "Challenges and Considerations", level: 2 },
    { id: "future", title: "Future Outlook", level: 2 }
  ]
};

const mockRelatedPosts = [
  {
    id: "2",
    title: "Machine Learning Best Practices for Small Businesses",
    excerpt: "Learn how to implement ML solutions without breaking the bank",
    image: "https://picsum.photos/seed/ml-practices/400/250",
    readingTime: 6,
    publishedAt: "2024-01-10",
    author: "Michael Rodriguez"
  },
  {
    id: "3",
    title: "Building AI-Powered Customer Service Solutions",
    excerpt: "Transform your customer support with intelligent automation",
    image: "https://picsum.photos/seed/ai-customer/400/250",
    readingTime: 7,
    publishedAt: "2024-01-08",
    author: "Emily Johnson"
  },
  {
    id: "4",
    title: "The Ethics of AI: Responsible Implementation Guide",
    excerpt: "Navigate the ethical considerations of AI deployment",
    image: "https://picsum.photos/seed/ai-ethics/400/250",
    readingTime: 9,
    publishedAt: "2024-01-05",
    author: "Dr. James Wilson"
  }
];



export default function BlogPostPage() {
  const [readingProgress, setReadingProgress] = useState(0);
  const [activeSection, setActiveSection] = useState("");
  const [liked, setLiked] = useState(false);
  const [fontSize, setFontSize] = useState(16);
  const [showShareMenu, setShowShareMenu] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [showMobileTOC, setShowMobileTOC] = useState(false);
  const [showReadingTools, setShowReadingTools] = useState(false);

  const contentRef = useRef<HTMLDivElement>(null);
  const shareButtonRef = useRef<HTMLButtonElement>(null);

  // Reading progress calculation
  useEffect(() => {
    const handleScroll = () => {
      if (contentRef.current) {
        const element = contentRef.current;
        const totalHeight = element.scrollHeight - element.clientHeight;
        const progress = (window.scrollY / totalHeight) * 100;
        setReadingProgress(Math.min(100, Math.max(0, progress)));

        // Show scroll to top button
        setShowScrollTop(window.scrollY > 500);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Active section tracking
  useEffect(() => {
    const handleScroll = () => {
      const sections = mockBlogPost.tableOfContents;
      const scrollPosition = window.scrollY + 100;

      for (let i = sections.length - 1; i >= 0; i--) {
        const element = document.getElementById(sections[i].id);
        if (element && element.offsetTop <= scrollPosition) {
          setActiveSection(sections[i].id);
          break;
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Click outside handler for share menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (shareButtonRef.current && !shareButtonRef.current.contains(event.target as Node)) {
        setShowShareMenu(false);
      }
    };

    if (showShareMenu) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showShareMenu]);

  const handleLike = () => {
    setLiked(!liked);
    // Add a small animation effect
    const button = document.querySelector('.like-button');
    if (button) {
      button.classList.add('animate-pulse-heart');
      setTimeout(() => button.classList.remove('animate-pulse-heart'), 300);
    }
  };



  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  };



  return (
    <div className="min-h-screen bg-gray-50" ref={contentRef}>
      {/* 📊 Reading Progress Bar */}
      <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 z-50">
        <div
          className="h-full bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-300 ease-out"
          style={{ width: `${readingProgress}%` }}
        />
      </div>

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 z-40 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
        >
          <ArrowUp className="w-5 h-5" />
        </button>
      )}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        {/* 🛠️ Mobile Reading Tools - Sticky Bar */}
        <div className="lg:hidden sticky top-16 z-30 mb-4">
          <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-gray-600 hidden sm:inline">Font</span>
                  <button
                    onClick={() => setFontSize(Math.max(12, fontSize - 2))}
                    className="p-1.5 hover:bg-gray-100 rounded transition-colors"
                    title="Decrease font size"
                  >
                    <Minus className="w-3 h-3" />
                  </button>
                  <span className="text-xs w-6 text-center font-medium">{fontSize}</span>
                  <button
                    onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                    className="p-1.5 hover:bg-gray-100 rounded transition-colors"
                    title="Increase font size"
                  >
                    <Plus className="w-3 h-3" />
                  </button>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <button
                  className="p-1.5 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Read aloud"
                >
                  <Volume2 className="w-4 h-4 text-gray-600" />
                </button>
                <button
                  className="p-1.5 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Print article"
                >
                  <Printer className="w-4 h-4 text-gray-600" />
                </button>
                <button
                  onClick={() => setShowMobileTOC(!showMobileTOC)}
                  className={`p-1.5 hover:bg-gray-100 rounded-lg transition-colors ${
                    showMobileTOC ? 'bg-blue-50 text-blue-600' : 'text-gray-600'
                  }`}
                  title="Table of contents"
                >
                  <BookOpen className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 📋 Mobile Table of Contents */}
        {showMobileTOC && (
          <div className="lg:hidden mb-6 bg-white rounded-xl p-4 shadow-sm border border-gray-100">
            <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
              <BookOpen className="w-4 h-4" />
              Table of Contents
            </h3>
            <nav className="space-y-2">
              {mockBlogPost.tableOfContents.map((item) => (
                <a
                  key={item.id}
                  href={`#${item.id}`}
                  onClick={() => setShowMobileTOC(false)}
                  className={`block text-sm transition-colors duration-200 py-1 ${
                    activeSection === item.id
                      ? "text-blue-600 font-medium"
                      : "text-gray-600 hover:text-gray-900"
                  } ${item.level === 3 ? "ml-4" : ""}`}
                >
                  {item.title}
                </a>
              ))}
            </nav>
          </div>
        )}

        {/* 📋 Floating Table of Contents - Desktop */}
        <div className="hidden xl:block fixed left-8 top-1/2 transform -translate-y-1/2 z-30">
          <div className="bg-white rounded-lg p-3 shadow-lg border border-gray-200 max-w-xs">
            <h3 className="font-medium text-gray-900 mb-3 text-sm">Contents</h3>
            <nav className="space-y-1">
              {mockBlogPost.tableOfContents.map((item) => (
                <a
                  key={item.id}
                  href={`#${item.id}`}
                  className={`block text-xs leading-relaxed py-1 transition-colors duration-200 ${
                    activeSection === item.id
                      ? "text-blue-600 font-medium"
                      : "text-gray-500 hover:text-gray-700"
                  } ${item.level === 3 ? "ml-3" : ""}`}
                >
                  {item.title}
                </a>
              ))}
            </nav>
          </div>
        </div>

        {/* 🛠️ Floating Reading Tools - Desktop */}
        <div className="hidden lg:block fixed right-8 top-1/2 transform -translate-y-1/2 z-30">
          <div className="bg-white rounded-lg p-3 shadow-lg border border-gray-200">
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2 mb-2">
                <button
                  onClick={() => setFontSize(Math.max(12, fontSize - 2))}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Decrease font size"
                >
                  <Minus className="w-4 h-4" />
                </button>
                <span className="text-sm font-medium w-8 text-center">{fontSize}</span>
                <button
                  onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Increase font size"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
              <button
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-600"
                title="Read aloud"
              >
                <Volume2 className="w-4 h-4" />
              </button>
              <button
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-600"
                title="Print article"
              >
                <Printer className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Main Content - Full Width */}
        <div className="max-w-4xl mx-auto">
          <main>
            <article className="bg-white">
              <div className="px-4 sm:px-6 md:px-8 py-8">
                {/* Categories */}
                <div className="flex flex-wrap gap-2 mb-6">
                  {mockBlogPost.categories.map((category) => (
                    <span
                      key={category}
                      className="text-sm text-blue-600 font-medium"
                    >
                      {category}
                    </span>
                  ))}
                </div>

                {/* Title */}
                <h1 className="text-3xl md:text-5xl font-bold text-gray-900 mb-8 leading-tight">
                  {mockBlogPost.title}
                </h1>

                {/* Author Info - Single Line */}
                <div className="flex items-center gap-4 mb-8 pb-8 border-b border-gray-200">
                  <Image
                    src={mockBlogPost.author.avatar}
                    alt={mockBlogPost.author.name}
                    width={48}
                    height={48}
                    className="rounded-full"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 text-sm">
                      <span className="font-medium text-gray-900">{mockBlogPost.author.name}</span>
                      <span className="text-gray-500">•</span>
                      <span className="text-gray-600">{formatDate(mockBlogPost.publishedAt)}</span>
                      <span className="text-gray-500">•</span>
                      <span className="text-gray-600">{mockBlogPost.readingTime} min read</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <button
                      onClick={handleLike}
                      className={`flex items-center gap-1 px-3 py-1.5 rounded-full text-sm transition-colors ${
                        liked
                          ? "bg-red-50 text-red-600"
                          : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                      }`}
                    >
                      <Heart className={`w-4 h-4 ${liked ? "fill-current" : ""}`} />
                      {mockBlogPost.likes + (liked ? 1 : 0)}
                    </button>
                    <div className="relative">
                      <button
                        onClick={() => setShowShareMenu(!showShareMenu)}
                        className="flex items-center gap-1 px-3 py-1.5 bg-gray-100 text-gray-600 hover:bg-gray-200 rounded-full text-sm transition-colors"
                      >
                        <Share2 className="w-4 h-4" />
                        Share
                      </button>
                    </div>
                  </div>
                </div>

                {/* Featured Image */}
                <div className="relative w-full h-64 md:h-96 mb-8 rounded-lg overflow-hidden">
                  <Image
                    src={mockBlogPost.featuredImage}
                    alt={mockBlogPost.title}
                    fill
                    className="object-cover"
                    priority
                  />
                </div>

                {/* Article Content */}
                <div
                  className="prose prose-lg max-w-none"
                  style={{ fontSize: `${fontSize}px` }}
                  dangerouslySetInnerHTML={{ __html: mockBlogPost.content }}
                />

                {/* Tags */}
                <div className="mt-8 pt-6 border-t border-gray-100">
                  <h3 className="text-sm font-semibold text-gray-900 mb-3">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {mockBlogPost.tags.map((tag) => (
                      <Link
                        key={tag}
                        href={`/community?tag=${encodeURIComponent(tag)}`}
                        className="inline-flex items-center gap-1 px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-colors"
                      >
                        <Tag className="w-3 h-3" />
                        {tag}
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            </article>



            {/* 🔗 Related Posts */}
            <section className="mt-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                <TrendingUp className="w-6 h-6" />
                Related Articles
              </h2>
              <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {mockRelatedPosts.map((post) => (
                  <Link
                    key={post.id}
                    href={`/community/${post.id}`}
                    className="group bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-300"
                  >
                    <div className="relative w-full h-48 bg-gray-100">
                      <Image
                        src={post.image}
                        alt={post.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                        {post.title}
                      </h3>
                      <p className="text-sm text-gray-600 mb-3">{post.excerpt}</p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{post.author}</span>
                        <div className="flex items-center gap-2">
                          <Clock className="w-3 h-3" />
                          {post.readingTime} min
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </section>
          </main>



        </div>
      </div>
    </div>
  );
}