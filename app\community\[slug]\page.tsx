"use client";

import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  Heart,
  Share2,
  Facebook,
  Twitter,
  Linkedin,
  Copy,
  Bookmark,
  Eye,
  Clock,
  Volume2,
  Printer,
  Minus,
  Plus,
  Tag,
  MessageCircle,
  ThumbsUp,
  Reply,
  Calendar,
  ExternalLink,
  BookOpen,
  TrendingUp,
  CheckCircle,
  Send,
  ArrowUp,
} from "lucide-react";

// Mock data - replace with your actual data fetching
const mockBlogPost = {
  id: "1",
  title: "The Future of AI in Business: A Comprehensive Guide to Digital Transformation",
  slug: "future-ai-business-digital-transformation",
  content: `
    <h2>Introduction to AI in Modern Business</h2>
    <p>Artificial Intelligence is revolutionizing the way businesses operate, from automating routine tasks to providing deep insights through data analysis. In this comprehensive guide, we'll explore how AI is reshaping industries and what it means for the future of work.</p>

    <h2>Key Benefits of AI Implementation</h2>
    <p>The implementation of AI technologies brings numerous advantages to businesses of all sizes. These benefits include increased efficiency, reduced operational costs, improved customer experiences, and enhanced decision-making capabilities.</p>

    <h3>Automation and Efficiency</h3>
    <p>AI-powered automation can handle repetitive tasks, freeing up human resources for more strategic work. This leads to significant productivity gains and allows employees to focus on creative and high-value activities.</p>

    <h3>Data-Driven Insights</h3>
    <p>Machine learning algorithms can analyze vast amounts of data to uncover patterns and trends that would be impossible for humans to detect manually. This enables businesses to make more informed decisions based on concrete evidence rather than intuition.</p>

    <h2>Real-World Applications</h2>
    <p>From chatbots providing 24/7 customer support to predictive analytics optimizing supply chains, AI applications are diverse and growing rapidly. Companies across industries are finding innovative ways to leverage AI for competitive advantage.</p>

    <h2>Challenges and Considerations</h2>
    <p>While AI offers tremendous opportunities, it also presents challenges including data privacy concerns, the need for skilled personnel, and potential job displacement. Organizations must carefully plan their AI adoption strategy to maximize benefits while mitigating risks.</p>

    <h2>Future Outlook</h2>
    <p>As AI technology continues to evolve, we can expect even more sophisticated applications and broader adoption across industries. The businesses that start their AI journey today will be best positioned to thrive in the digital future.</p>
  `,
  excerpt: "Discover how artificial intelligence is transforming modern business operations and learn practical strategies for successful AI implementation in your organization.",
  publishedAt: "2024-01-15",
  readingTime: 8,
  views: 12547,
  likes: 342,
  bookmarks: 89,
  author: {
    id: "1",
    name: "Dr. Sarah Chen",
    bio: "AI Research Director with 15+ years of experience in machine learning and business transformation. Former Google AI researcher, now helping companies navigate their digital transformation journey.",
    avatar: "https://i.pravatar.cc/150?u=sarah-chen",
    role: "AI Research Director",
    company: "Kodaze",
    location: "San Francisco, CA",
    experience: "15+ years",
    articles: 47,
    followers: 12500,
    verified: true,
    social: {
      twitter: "https://twitter.com/sarahchen",
      linkedin: "https://linkedin.com/in/sarahchen",
      website: "https://sarahchen.ai"
    },
    expertise: ["Machine Learning", "Business Strategy", "Digital Transformation", "AI Ethics"]
  },
  categories: ["AI & Technology", "Business Strategy", "Digital Transformation"],
  tags: ["Artificial Intelligence", "Machine Learning", "Business Automation", "Digital Innovation", "Future of Work"],
  featuredImage: "https://picsum.photos/seed/ai-business/1200/600",
  tableOfContents: [
    { id: "introduction", title: "Introduction to AI in Modern Business", level: 2 },
    { id: "benefits", title: "Key Benefits of AI Implementation", level: 2 },
    { id: "automation", title: "Automation and Efficiency", level: 3 },
    { id: "insights", title: "Data-Driven Insights", level: 3 },
    { id: "applications", title: "Real-World Applications", level: 2 },
    { id: "challenges", title: "Challenges and Considerations", level: 2 },
    { id: "future", title: "Future Outlook", level: 2 }
  ]
};

const mockRelatedPosts = [
  {
    id: "2",
    title: "Machine Learning Best Practices for Small Businesses",
    excerpt: "Learn how to implement ML solutions without breaking the bank",
    image: "https://picsum.photos/seed/ml-practices/400/250",
    readingTime: 6,
    publishedAt: "2024-01-10",
    author: "Michael Rodriguez"
  },
  {
    id: "3",
    title: "Building AI-Powered Customer Service Solutions",
    excerpt: "Transform your customer support with intelligent automation",
    image: "https://picsum.photos/seed/ai-customer/400/250",
    readingTime: 7,
    publishedAt: "2024-01-08",
    author: "Emily Johnson"
  },
  {
    id: "4",
    title: "The Ethics of AI: Responsible Implementation Guide",
    excerpt: "Navigate the ethical considerations of AI deployment",
    image: "https://picsum.photos/seed/ai-ethics/400/250",
    readingTime: 9,
    publishedAt: "2024-01-05",
    author: "Dr. James Wilson"
  }
];

const mockComments = [
  {
    id: "1",
    author: "Alex Thompson",
    avatar: "https://i.pravatar.cc/40?u=alex",
    content: "Excellent article! The section on data-driven insights really resonated with our experience implementing AI in our marketing department.",
    publishedAt: "2024-01-16T10:30:00Z",
    likes: 12,
    isAuthor: false,
    replies: [
      {
        id: "1-1",
        author: "Dr. Sarah Chen",
        avatar: "https://i.pravatar.cc/40?u=sarah-chen",
        content: "Thank you Alex! I'd love to hear more about your marketing AI implementation. Feel free to reach out if you'd like to discuss it further.",
        publishedAt: "2024-01-16T14:20:00Z",
        likes: 8,
        isAuthor: true
      }
    ]
  },
  {
    id: "2",
    author: "Maria Garcia",
    avatar: "https://i.pravatar.cc/40?u=maria",
    content: "This is exactly what I needed to convince our leadership team about AI adoption. The ROI examples are particularly compelling.",
    publishedAt: "2024-01-16T09:15:00Z",
    likes: 18,
    isAuthor: false,
    replies: []
  },
  {
    id: "3",
    author: "David Kim",
    avatar: "https://i.pravatar.cc/40?u=david",
    content: "Great insights on the challenges section. We're currently facing some of these issues in our implementation. Any recommendations for training resources?",
    publishedAt: "2024-01-15T16:45:00Z",
    likes: 7,
    isAuthor: false,
    replies: [
      {
        id: "3-1",
        author: "Jennifer Liu",
        avatar: "https://i.pravatar.cc/40?u=jennifer",
        content: "I recommend checking out the AI courses on Coursera and edX. They have some excellent programs for business professionals.",
        publishedAt: "2024-01-15T18:30:00Z",
        likes: 5,
        isAuthor: false
      }
    ]
  }
];

export default function BlogPostPage() {
  const [readingProgress, setReadingProgress] = useState(0);
  const [activeSection, setActiveSection] = useState("");
  const [liked, setLiked] = useState(false);
  const [bookmarked, setBookmarked] = useState(false);
  const [fontSize, setFontSize] = useState(16);
  const [showShareMenu, setShowShareMenu] = useState(false);
  const [showCommentForm, setShowCommentForm] = useState(false);
  const [newComment, setNewComment] = useState("");
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [showMobileTOC, setShowMobileTOC] = useState(false);

  const contentRef = useRef<HTMLDivElement>(null);
  const shareButtonRef = useRef<HTMLButtonElement>(null);

  // Reading progress calculation
  useEffect(() => {
    const handleScroll = () => {
      if (contentRef.current) {
        const element = contentRef.current;
        const totalHeight = element.scrollHeight - element.clientHeight;
        const progress = (window.scrollY / totalHeight) * 100;
        setReadingProgress(Math.min(100, Math.max(0, progress)));

        // Show scroll to top button
        setShowScrollTop(window.scrollY > 500);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Active section tracking
  useEffect(() => {
    const handleScroll = () => {
      const sections = mockBlogPost.tableOfContents;
      const scrollPosition = window.scrollY + 100;

      for (let i = sections.length - 1; i >= 0; i--) {
        const element = document.getElementById(sections[i].id);
        if (element && element.offsetTop <= scrollPosition) {
          setActiveSection(sections[i].id);
          break;
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Click outside handler for share menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (shareButtonRef.current && !shareButtonRef.current.contains(event.target as Node)) {
        setShowShareMenu(false);
      }
    };

    if (showShareMenu) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showShareMenu]);

  const handleLike = () => {
    setLiked(!liked);
    // Add a small animation effect
    const button = document.querySelector('.like-button');
    if (button) {
      button.classList.add('animate-pulse-heart');
      setTimeout(() => button.classList.remove('animate-pulse-heart'), 300);
    }
  };

  const handleBookmark = () => {
    setBookmarked(!bookmarked);
  };

  const handleShare = (platform: string) => {
    const url = window.location.href;
    const title = mockBlogPost.title;

    switch (platform) {
      case "twitter":
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`);
        break;
      case "facebook":
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`);
        break;
      case "linkedin":
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`);
        break;
      case "copy":
        navigator.clipboard.writeText(url);
        // You could add a toast notification here
        break;
    }
    setShowShareMenu(false);
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  };

  const timeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50" ref={contentRef}>
      {/* 📊 Reading Progress Bar */}
      <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 z-50">
        <div
          className="h-full bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-300 ease-out"
          style={{ width: `${readingProgress}%` }}
        />
      </div>

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 z-40 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
        >
          <ArrowUp className="w-5 h-5" />
        </button>
      )}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20">
        {/* 🛠️ Mobile Reading Tools - Sticky Bar */}
        <div className="lg:hidden sticky top-16 z-30 mb-4">
          <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-gray-600 hidden sm:inline">Font</span>
                  <button
                    onClick={() => setFontSize(Math.max(12, fontSize - 2))}
                    className="p-1.5 hover:bg-gray-100 rounded transition-colors"
                    title="Decrease font size"
                  >
                    <Minus className="w-3 h-3" />
                  </button>
                  <span className="text-xs w-6 text-center font-medium">{fontSize}</span>
                  <button
                    onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                    className="p-1.5 hover:bg-gray-100 rounded transition-colors"
                    title="Increase font size"
                  >
                    <Plus className="w-3 h-3" />
                  </button>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <button
                  className="p-1.5 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Read aloud"
                >
                  <Volume2 className="w-4 h-4 text-gray-600" />
                </button>
                <button
                  className="p-1.5 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Print article"
                >
                  <Printer className="w-4 h-4 text-gray-600" />
                </button>
                <button
                  onClick={() => setShowMobileTOC(!showMobileTOC)}
                  className={`p-1.5 hover:bg-gray-100 rounded-lg transition-colors ${
                    showMobileTOC ? 'bg-blue-50 text-blue-600' : 'text-gray-600'
                  }`}
                  title="Table of contents"
                >
                  <BookOpen className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 📋 Mobile Table of Contents */}
        {showMobileTOC && (
          <div className="lg:hidden mb-6 bg-white rounded-xl p-4 shadow-sm border border-gray-100">
            <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
              <BookOpen className="w-4 h-4" />
              Table of Contents
            </h3>
            <nav className="space-y-2">
              {mockBlogPost.tableOfContents.map((item) => (
                <a
                  key={item.id}
                  href={`#${item.id}`}
                  onClick={() => setShowMobileTOC(false)}
                  className={`block text-sm transition-colors duration-200 py-1 ${
                    activeSection === item.id
                      ? "text-blue-600 font-medium"
                      : "text-gray-600 hover:text-gray-900"
                  } ${item.level === 3 ? "ml-4" : ""}`}
                >
                  {item.title}
                </a>
              ))}
            </nav>
          </div>
        )}

        <div className="grid lg:grid-cols-12 gap-8">
          {/* 📋 Table of Contents - Desktop Sidebar */}
          <aside className="hidden lg:block lg:col-span-3 xl:col-span-2">
            <div className="sticky top-24 space-y-8">
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <h3 className="font-semibold text-gray-900 mb-5 flex items-center gap-2">
                  <BookOpen className="w-4 h-4" />
                  Table of Contents
                </h3>
                <nav className="space-y-3">
                  {mockBlogPost.tableOfContents.map((item) => (
                    <a
                      key={item.id}
                      href={`#${item.id}`}
                      className={`block text-sm leading-relaxed py-1 transition-colors duration-200 ${
                        activeSection === item.id
                          ? "text-blue-600 font-medium"
                          : "text-gray-600 hover:text-gray-900"
                      } ${item.level === 3 ? "ml-4" : ""}`}
                    >
                      {item.title}
                    </a>
                  ))}
                </nav>
              </div>

              {/* 🛠️ Reading Tools - Desktop */}
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <h3 className="font-semibold text-gray-900 mb-5">Reading Tools</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between gap-3">
                    <span className="text-sm text-gray-600 whitespace-nowrap">Font Size</span>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setFontSize(Math.max(12, fontSize - 2))}
                        className="p-1.5 hover:bg-gray-100 rounded transition-colors"
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                      <span className="text-sm w-8 text-center font-medium">{fontSize}</span>
                      <button
                        onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                        className="p-1.5 hover:bg-gray-100 rounded transition-colors"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  <button className="w-full flex items-center gap-3 text-sm text-gray-600 hover:text-gray-900 py-2.5 px-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <Volume2 className="w-4 h-4" />
                    Read Aloud
                  </button>
                  <button className="w-full flex items-center gap-3 text-sm text-gray-600 hover:text-gray-900 py-2.5 px-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <Printer className="w-4 h-4" />
                    Print Article
                  </button>
                </div>
              </div>
            </div>
          </aside>

          {/* Main Content */}
          <main className="lg:col-span-6 xl:col-span-7 order-1 lg:order-none">
            <article className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              {/* Featured Image */}
              <div className="relative w-full h-64 md:h-80 bg-gray-100">
                <Image
                  src={mockBlogPost.featuredImage}
                  alt={mockBlogPost.title}
                  fill
                  className="object-cover"
                  priority
                />
              </div>

              <div className="p-4 sm:p-6 md:p-8">
                {/* Categories */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {mockBlogPost.categories.map((category) => (
                    <span
                      key={category}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-blue-50 text-blue-700 text-sm font-medium rounded-full"
                    >
                      <Tag className="w-3 h-3" />
                      {category}
                    </span>
                  ))}
                </div>

                {/* Title */}
                <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 leading-tight">
                  {mockBlogPost.title}
                </h1>

                {/* Meta Information */}
                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b border-gray-100">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    {formatDate(mockBlogPost.publishedAt)}
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    {mockBlogPost.readingTime} min read
                  </div>
                  <div className="flex items-center gap-2">
                    <Eye className="w-4 h-4" />
                    {mockBlogPost.views.toLocaleString()} views
                  </div>
                  <div className="flex items-center gap-2">
                    <Heart className="w-4 h-4" />
                    {mockBlogPost.likes} likes
                  </div>
                </div>

                {/* Article Content */}
                <div
                  className="prose prose-lg max-w-none"
                  style={{ fontSize: `${fontSize}px` }}
                  dangerouslySetInnerHTML={{ __html: mockBlogPost.content }}
                />

                {/* Tags */}
                <div className="mt-8 pt-6 border-t border-gray-100">
                  <h3 className="text-sm font-semibold text-gray-900 mb-3">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {mockBlogPost.tags.map((tag) => (
                      <Link
                        key={tag}
                        href={`/community?tag=${encodeURIComponent(tag)}`}
                        className="inline-flex items-center gap-1 px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-colors"
                      >
                        <Tag className="w-3 h-3" />
                        {tag}
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            </article>

            {/* 💬 Comments Section */}
            <section className="mt-8 bg-white rounded-xl shadow-sm border border-gray-100 p-4 sm:p-6 md:p-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                  <MessageCircle className="w-6 h-6" />
                  Comments ({mockComments.length})
                </h2>
                <button
                  onClick={() => setShowCommentForm(!showCommentForm)}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  <Send className="w-4 h-4" />
                  Add Comment
                </button>
              </div>

              {/* Comment Form */}
              {showCommentForm && (
                <div className="mb-8 p-4 bg-gray-50 rounded-lg">
                  <textarea
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    placeholder="Share your thoughts..."
                    className="w-full p-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={4}
                  />
                  <div className="flex justify-end gap-2 mt-3">
                    <button
                      onClick={() => setShowCommentForm(false)}
                      className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                    >
                      Cancel
                    </button>
                    <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                      Post Comment
                    </button>
                  </div>
                </div>
              )}

              {/* Comments List */}
              <div className="space-y-6">
                {mockComments.map((comment) => (
                  <div key={comment.id} className="border-b border-gray-100 pb-6 last:border-b-0">
                    <div className="flex gap-4">
                      <Image
                        src={comment.avatar}
                        alt={comment.author}
                        width={40}
                        height={40}
                        className="rounded-full"
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-semibold text-gray-900">{comment.author}</h4>
                          {comment.isAuthor && (
                            <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full flex items-center gap-1">
                              <CheckCircle className="w-3 h-3" />
                              Author
                            </span>
                          )}
                          <span className="text-sm text-gray-500">
                            {timeAgo(comment.publishedAt)}
                          </span>
                        </div>
                        <p className="text-gray-700 mb-3">{comment.content}</p>
                        <div className="flex items-center gap-4">
                          <button className="flex items-center gap-1 text-sm text-gray-500 hover:text-blue-600 transition-colors">
                            <ThumbsUp className="w-4 h-4" />
                            {comment.likes}
                          </button>
                          <button className="flex items-center gap-1 text-sm text-gray-500 hover:text-blue-600 transition-colors">
                            <Reply className="w-4 h-4" />
                            Reply
                          </button>
                        </div>

                        {/* Replies */}
                        {comment.replies && comment.replies.length > 0 && (
                          <div className="mt-4 ml-6 space-y-4">
                            {comment.replies.map((reply) => (
                              <div key={reply.id} className="flex gap-3">
                                <Image
                                  src={reply.avatar}
                                  alt={reply.author}
                                  width={32}
                                  height={32}
                                  className="rounded-full"
                                />
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-1">
                                    <h5 className="font-medium text-gray-900 text-sm">{reply.author}</h5>
                                    {reply.isAuthor && (
                                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full flex items-center gap-1">
                                        <CheckCircle className="w-3 h-3" />
                                        Author
                                      </span>
                                    )}
                                    <span className="text-xs text-gray-500">
                                      {timeAgo(reply.publishedAt)}
                                    </span>
                                  </div>
                                  <p className="text-gray-700 text-sm mb-2">{reply.content}</p>
                                  <button className="flex items-center gap-1 text-xs text-gray-500 hover:text-blue-600 transition-colors">
                                    <ThumbsUp className="w-3 h-3" />
                                    {reply.likes}
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* 🔗 Related Posts */}
            <section className="mt-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                <TrendingUp className="w-6 h-6" />
                Related Articles
              </h2>
              <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {mockRelatedPosts.map((post) => (
                  <Link
                    key={post.id}
                    href={`/community/${post.id}`}
                    className="group bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-300"
                  >
                    <div className="relative w-full h-48 bg-gray-100">
                      <Image
                        src={post.image}
                        alt={post.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                        {post.title}
                      </h3>
                      <p className="text-sm text-gray-600 mb-3">{post.excerpt}</p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{post.author}</span>
                        <div className="flex items-center gap-2">
                          <Clock className="w-3 h-3" />
                          {post.readingTime} min
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </section>
          </main>

          {/* Right Sidebar */}
          <aside className="lg:col-span-3 xl:col-span-3 order-2 lg:order-none">
            <div className="lg:sticky lg:top-24 space-y-6">
              {/* 👤 Author Bio */}
              <div className="bg-white rounded-xl p-4 sm:p-6 shadow-sm border border-gray-100">
                <div className="text-center">
                  <div className="relative inline-block mb-4">
                    <Image
                      src={mockBlogPost.author.avatar}
                      alt={mockBlogPost.author.name}
                      width={80}
                      height={80}
                      className="rounded-full"
                    />
                    {mockBlogPost.author.verified && (
                      <div className="absolute -bottom-1 -right-1 bg-blue-600 rounded-full p-1">
                        <CheckCircle className="w-4 h-4 text-white" />
                      </div>
                    )}
                  </div>
                  <h3 className="font-bold text-gray-900 mb-1">{mockBlogPost.author.name}</h3>
                  <p className="text-sm text-gray-600 mb-2">{mockBlogPost.author.role}</p>
                  <p className="text-xs text-gray-500 mb-4">{mockBlogPost.author.company} • {mockBlogPost.author.location}</p>

                  <div className="grid grid-cols-3 gap-4 mb-4 text-center">
                    <div>
                      <div className="font-semibold text-gray-900">{mockBlogPost.author.articles}</div>
                      <div className="text-xs text-gray-500">Articles</div>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{(mockBlogPost.author.followers / 1000).toFixed(1)}K</div>
                      <div className="text-xs text-gray-500">Followers</div>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{mockBlogPost.author.experience}</div>
                      <div className="text-xs text-gray-500">Experience</div>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-4">{mockBlogPost.author.bio}</p>

                  {/* Expertise Tags */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    {mockBlogPost.author.expertise.map((skill) => (
                      <span
                        key={skill}
                        className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>

                  {/* Social Links */}
                  <div className="flex justify-center gap-3">
                    <a
                      href={mockBlogPost.author.social.twitter}
                      className="p-2 text-gray-400 hover:text-blue-500 transition-colors"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Twitter className="w-4 h-4" />
                    </a>
                    <a
                      href={mockBlogPost.author.social.linkedin}
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Linkedin className="w-4 h-4" />
                    </a>
                    <a
                      href={mockBlogPost.author.social.website}
                      className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </a>
                  </div>
                </div>
              </div>

              {/* 📱 Social Sharing & Actions */}
              <div className="bg-white rounded-xl p-4 sm:p-6 shadow-sm border border-gray-100">
                <h3 className="font-semibold text-gray-900 mb-4">Share & Save</h3>
                <div className="space-y-3">
                  {/* Like Button */}
                  <button
                    onClick={handleLike}
                    className={`like-button w-full flex items-center justify-center gap-2 py-3 px-4 rounded-lg border transition-all duration-300 hover-lift ${
                      liked
                        ? "bg-red-50 border-red-200 text-red-600"
                        : "bg-gray-50 border-gray-200 text-gray-600 hover:bg-red-50 hover:border-red-200 hover:text-red-600"
                    }`}
                  >
                    <Heart className={`w-5 h-5 ${liked ? "fill-current" : ""}`} />
                    {liked ? "Liked" : "Like"} ({mockBlogPost.likes + (liked ? 1 : 0)})
                  </button>

                  {/* Bookmark Button */}
                  <button
                    onClick={handleBookmark}
                    className={`w-full flex items-center justify-center gap-2 py-3 px-4 rounded-lg border transition-all duration-300 ${
                      bookmarked
                        ? "bg-blue-50 border-blue-200 text-blue-600"
                        : "bg-gray-50 border-gray-200 text-gray-600 hover:bg-blue-50 hover:border-blue-200 hover:text-blue-600"
                    }`}
                  >
                    <Bookmark className={`w-5 h-5 ${bookmarked ? "fill-current" : ""}`} />
                    {bookmarked ? "Bookmarked" : "Bookmark"}
                  </button>

                  {/* Share Button */}
                  <div className="relative">
                    <button
                      ref={shareButtonRef}
                      onClick={() => setShowShareMenu(!showShareMenu)}
                      className="w-full flex items-center justify-center gap-2 py-3 px-4 rounded-lg border bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100 transition-colors"
                    >
                      <Share2 className="w-5 h-5" />
                      Share Article
                    </button>

                    {/* Share Menu */}
                    {showShareMenu && (
                      <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                        <div className="p-2">
                          <button
                            onClick={() => handleShare("twitter")}
                            className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md"
                          >
                            <Twitter className="w-4 h-4 text-blue-400" />
                            Share on Twitter
                          </button>
                          <button
                            onClick={() => handleShare("facebook")}
                            className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md"
                          >
                            <Facebook className="w-4 h-4 text-blue-600" />
                            Share on Facebook
                          </button>
                          <button
                            onClick={() => handleShare("linkedin")}
                            className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md"
                          >
                            <Linkedin className="w-4 h-4 text-blue-700" />
                            Share on LinkedIn
                          </button>
                          <button
                            onClick={() => handleShare("copy")}
                            className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md"
                          >
                            <Copy className="w-4 h-4 text-gray-500" />
                            Copy Link
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 📈 Article Stats */}
              <div className="bg-white rounded-xl p-4 sm:p-6 shadow-sm border border-gray-100">
                <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Article Stats
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 flex items-center gap-2">
                      <Eye className="w-4 h-4" />
                      Views
                    </span>
                    <span className="font-semibold text-gray-900">
                      {mockBlogPost.views.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 flex items-center gap-2">
                      <Heart className="w-4 h-4" />
                      Likes
                    </span>
                    <span className="font-semibold text-gray-900">
                      {mockBlogPost.likes + (liked ? 1 : 0)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 flex items-center gap-2">
                      <Bookmark className="w-4 h-4" />
                      Bookmarks
                    </span>
                    <span className="font-semibold text-gray-900">
                      {mockBlogPost.bookmarks + (bookmarked ? 1 : 0)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 flex items-center gap-2">
                      <MessageCircle className="w-4 h-4" />
                      Comments
                    </span>
                    <span className="font-semibold text-gray-900">
                      {mockComments.length}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
}