import Link from "next/link";
import { Linkedin, Twitter, Github, Mail } from "lucide-react";
import Image from "next/image";
import kodazeLogo from "@/assets/Layout/kodaze-logo.png";

type NavItem = { label: string; href: string };

const company: NavItem[] = [
  { label: "About Us", href: "/about" },
  { label: "Services", href: "/services" },
  { label: "Community", href: "/community" },
  { label: "Contact", href: "/contact" },
];

const resources: NavItem[] = [
  { label: "Blog", href: "/blog" },
  { label: "Case Studies", href: "/case-studies" },
  { label: "AI Tools", href: "/tools" },
  { label: "Documentation", href: "/docs" },
];

const legal: NavItem[] = [
  { label: "Privacy Policy", href: "/privacy" },
  { label: "Terms of Service", href: "/terms" },
  { label: "Cookie Policy", href: "/cookies" },
];

export default function Footer() {
  return (
    <footer className="bg-white text-slate-700 border border-t border-slate-100">
      <div className="max-w-7xl mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-8">
          {/* Left column (logo + description + socials) */}
          <div className="md:col-span-5 lg:col-span-4">
            <div className="mb-6">
              <Link
                href="/"
                className="flex items-center gap-2 font-bold text-xl"
              >
                <Image
                  src={kodazeLogo}
                  alt="Kodaze Logo"
                  width={150}
                  height={150}
                />
              </Link>
            </div>

            <p className="text-sm text-slate-500 mb-6">
              Empowering businesses with AI-powered solutions and marketing
              technology. Transform your operations, accelerate growth, and stay
              ahead of the competition.
            </p>

            <div className="flex items-center gap-3">
              {/* Social icon buttons */}
              <a
                href="https://www.linkedin.com"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="LinkedIn"
                className="inline-flex items-center justify-center w-10 h-10 rounded-lg border border-slate-100 bg-white text-slate-700 hover:shadow transition"
              >
                <Linkedin size={16} />
              </a>

              <a
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Twitter"
                className="inline-flex items-center justify-center w-10 h-10 rounded-lg border border-slate-100 bg-white text-slate-700 hover:shadow transition"
              >
                <Twitter size={16} />
              </a>

              <a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="GitHub"
                className="inline-flex items-center justify-center w-10 h-10 rounded-lg border border-slate-100 bg-white text-slate-700 hover:shadow transition"
              >
                <Github size={16} />
              </a>

              <a
                href="mailto:<EMAIL>"
                aria-label="Email"
                className="inline-flex items-center justify-center w-10 h-10 rounded-lg border border-slate-100 bg-white text-slate-700 hover:shadow transition"
              >
                <Mail size={16} />
              </a>
            </div>
          </div>

          {/* Middle / right columns */}
          <div className="md:col-span-7 lg:col-span-8">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
              <div>
                <h4 className="text-sm font-semibold text-slate-900 mb-3">
                  Company
                </h4>
                <ul className="space-y-2">
                  {company.map((item) => (
                    <li key={item.href}>
                      <Link
                        href={item.href}
                        className="text-sm text-slate-600 hover:text-slate-900"
                      >
                        {item.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="text-sm font-semibold text-slate-900 mb-3">
                  Resources
                </h4>
                <ul className="space-y-2">
                  {resources.map((item) => (
                    <li key={item.href}>
                      <Link
                        href={item.href}
                        className="text-sm text-slate-600 hover:text-slate-900"
                      >
                        {item.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="text-sm font-semibold text-slate-900 mb-3">
                  Legal
                </h4>
                <ul className="space-y-2">
                  {legal.map((item) => (
                    <li key={item.href}>
                      <Link
                        href={item.href}
                        className="text-sm text-slate-600 hover:text-slate-900"
                      >
                        {item.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-slate-100 mt-10" />

        {/* Bottom row */}
        <div className="mt-6 flex flex-col-reverse md:flex-row items-center justify-between gap-4 text-sm text-slate-500">
          <div>© {new Date().getFullYear()} Kodaze. All rights reserved.</div>
          <div className="text-center md:text-right text-slate-400">
            Built with AI technology and modern design principles.
          </div>
        </div>
      </div>
    </footer>
  );
}
