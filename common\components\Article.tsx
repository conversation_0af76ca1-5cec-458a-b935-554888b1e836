import { Calendar, Clock } from "lucide-react";
import Image from "next/image";

function Article() {
  return (
    <article className="bg-white rounded-2xl shadow-md overflow-hidden border border-gray-100">
      {/* Image */}
      <div className="relative w-full h-44 sm:h-52 bg-gray-100">
        <Image
          src={"https://picsum.photos/seed/42/1200/800"}
          alt={"title"}
          fill
          sizes="(max-width: 640px) 100vw, 33vw"
          className="object-cover"
          priority={false}
        />
      </div>

      <div className="p-5">
        {/* badge + date */}
        <div className="flex items-center justify-between gap-3 mb-3">
          <div className="flex gap-2 flex-wrap">
            <span className="inline-block text-xs font-medium px-3 py-1 rounded-full bg-gray-100 text-gray-800">
              Category
            </span>
            <span className="inline-block text-xs font-medium px-3 py-1 rounded-full bg-gray-100 text-gray-800">
              Category
            </span>
            <span className="inline-block text-xs font-medium px-3 py-1 rounded-full bg-gray-100 text-gray-800">
              Category
            </span>
          </div>

          <div className="flex items-center gap-2 text-xs text-gray-500">
            <Calendar size={14} />
            <time dateTime={"1/12/2024"}>1/12/2024</time>
          </div>
        </div>

        {/* title */}
        <h3 className="text-gray-900 font-semibold text-lg leading-snug mb-3">
          How Small Businesses Can Leverage AI Without Breaking the Bank
        </h3>

        {/* excerpt */}
        <p className="text-sm text-gray-500 mb-4">
          Practical strategies for implementing cost-effective AI solutions that
          deliver real results.
        </p>

        {/* tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          <span
            // key={t}
            className="text-xs px-2 py-1 border border-gray-200 rounded-full text-gray-600"
          >
            Tag
          </span>
          <span
            // key={t}
            className="text-xs px-2 py-1 border border-gray-200 rounded-full text-gray-600"
          >
            Tag
          </span>
          <span
            // key={t}
            className="text-xs px-2 py-1 border border-gray-200 rounded-full text-gray-600"
          >
            Tag
          </span>
        </div>

        <div className="flex items-center justify-between text-sm text-gray-500">
          {/* author */}
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 relative">
              {/* small avatar from pravatar; uses author string to vary */}
              <Image
                src={`https://i.pravatar.cc/40?u=${encodeURIComponent(
                  "Michael Rodriguez"
                )}`}
                alt={"Michael Rodriguez"}
                fill
                className="object-cover"
              />
            </div>

            <div>
              <div className="text-gray-700 text-sm font-medium">
                Michael Rodriguez
              </div>
            </div>
          </div>

          {/* read time */}
          <div className="flex items-center gap-2">
            <Clock size={14} />
            <span className="text-xs text-gray-500">6 min read</span>
          </div>
        </div>
      </div>
    </article>
  );
}

export default Article;
