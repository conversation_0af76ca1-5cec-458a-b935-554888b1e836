import { Mail, Sparkles } from "lucide-react";

function Newsletter() {
  return (
    <section className="py-16 px-4">
      <div className="max-w-4xl mx-auto">
        <div
          className="rounded-2xl p-8 md:p-12 text-center shadow-xl overflow-hidden"
        >
          <div className="flex flex-col items-center gap-4">
            <div className="flex items-center gap-2 text-sm opacity-90">
              <Sparkles size={18} />
              <span className="font-medium">Stay Updated</span>
            </div>

            <h2 className="mt-1 text-2xl md:text-3xl lg:text-4xl font-extrabold">
              Get the Latest AI Insights
            </h2>

            <p className="max-w-2xl text-sm md:text-base opacity-90 mt-3">
              Join thousands of business leaders receiving weekly updates on AI
              trends, marketing strategies, and automation tools that drive
              growth.
            </p>

            <form
            //   onSubmit={handleSubmit}
              className="w-full mt-6 max-w-2xl"
              aria-label="Subscribe to newsletter"
            >
              <div className="flex flex-col sm:flex-row items-center gap-3">
                <label htmlFor="email" className="sr-only">
                  Email address
                </label>

                {/* input */}
                <input
                  id="email"
                  type="email"
                //   value={email}
                //   onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  required
                //   aria-invalid={!!errorMsg}
                  className="w-full sm:flex-1 px-4 py-3 rounded-lg border border-gray-300 outline-none transition"
                />

                {/* submit */}
                <button
                  type="submit"
                  aria-label="Subscribe"
                //   disabled={status === "sending"}
                  className="inline-flex items-center gap-2 px-4 py-3 rounded-lg bg-white  font-semibold shadow hover:opacity-95 disabled:opacity-60"
                >
                  <Mail size={16} />
                  <span>Subscribe</span>
                </button>
              </div>

              {/* feedback / note */}
              {/* <div className="mt-4 text-xs opacity-80">
                <div className="min-h-5">
                  {status === "success" && (
                    <p role="status" className="text-white/90">
                      ✅ Thank you — you’re subscribed!
                    </p>
                  )}
                  {status === "sending" && (
                    <p role="status" className="text-white/90">
                      Subscribing...
                    </p>
                  )}
                  {errorMsg && status === "error" && (
                    <p role="alert" className="text-yellow-200">
                      {errorMsg}
                    </p>
                  )}
                </div>

                <p className="mt-3 text-xs opacity-80">
                  We respect your privacy. Unsubscribe at any time.
                </p>
              </div> */}
            </form>
          </div>
        </div>
      </div>
    </section>
  );
}

export default Newsletter;
