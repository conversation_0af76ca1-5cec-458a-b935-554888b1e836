import { Linkedin, Twitter, Github } from "lucide-react";

function ConnectCard() {
  return (
    <div className="bg-white rounded-xl border border-slate-100 p-6 shadow-sm">
      <h4 className="text-sm font-semibold text-slate-900">Connect With Us</h4>
      <p className="mt-2 text-sm text-slate-500">
        Follow us on social media for the latest AI insights and updates.
      </p>

      <div className="mt-4 flex items-center gap-3">
        <div className="w-10 h-10 rounded-lg flex items-center justify-center bg-[#F3F1FF]">
          <Linkedin size={16} />
        </div>
        <div className="w-10 h-10 rounded-lg flex items-center justify-center bg-[#F3F1FF]">
          <Twitter size={16} />
        </div>
        <div className="w-10 h-10 rounded-lg flex items-center justify-center bg-[#F3F1FF]">
          <Github size={16} />
        </div>
      </div>
    </div>
  );
}

export default ConnectCard;
