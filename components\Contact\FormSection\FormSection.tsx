import Form from "./Form";
import QuickResponseCard from "./QuickResponseCard";
import ConnectCard from "./ConnectCard";
import ConsultationCTA from "./ConsultationCTA";

export default function FormSection() {
  return (
    <section className="py-16 px-6 bg-gray-50">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* LEFT: Form (visual only) */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl border border-slate-100 p-6 shadow-sm">
              <h3 className="text-xl font-semibold text-slate-900">
                Send us a Message
              </h3>
              <p className="mt-2 text-sm text-slate-500">
                Fill out the form below and we&apos;ll get back to you as soon
                as possible.
              </p>

              <Form />
            </div>
          </div>

          {/* RIGHT: stacked info cards (UI-only) */}
          <div className="space-y-6">
            <QuickResponseCard />
            <ConnectCard />
            <ConsultationCTA />
          </div>
        </div>
      </div>
    </section>
  );
}
