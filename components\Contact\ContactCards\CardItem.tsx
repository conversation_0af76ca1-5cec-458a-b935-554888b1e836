import { Card } from "../contact-types";

function CardItem({ c }: { c: Card }) {
  return (
    <article
      className="bg-white rounded-xl border border-slate-100 p-6 text-center shadow-sm hover:shadow-md transition flex flex-col justify-between"
      aria-labelledby={`contact-${c.id}-title`}
      role="group"
    >
      <div>
        <div className="mx-auto w-12 h-12 rounded-lg flex items-center justify-center mb-4 bg-[#F3F1FF]">
          <c.Icon size={18} />
        </div>

        <h3
          id={`contact-${c.id}-title`}
          className="text-sm font-semibold text-slate-900 mb-2"
        >
          {c.title}
        </h3>

        <p className="text-xs text-slate-500 mb-3">{c.subtitle}</p>
      </div>

      <div className="mt-2">
        {c.cta.href ? (
          c.cta.href.startsWith("http") ? (
            <a
              href={c.cta.href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm font-medium hover:underline"
            >
              {c.cta.label}
            </a>
          ) : (
            <a
              href={c.cta.href}
              className="text-sm font-medium hover:underline"
            >
              {c.cta.label}
            </a>
          )
        ) : (
          <span className="text-sm font-medium">
            {c.cta.label}
          </span>
        )}
      </div>
    </article>
  );
}

export default CardItem;
