import { PrivacyCard } from "../privacy-types";

function CardItem({ id, icon: Icon, title, content }: PrivacyCard) {
  return (
    <div
      key={id}
      className="bg-white rounded-xl border border-slate-200 p-6 shadow-sm"
    >
      <div className="flex items-center gap-3 mb-4">
        <div className="w-10 h-10 flex items-center justify-center rounded-lg bg-[#F3F1FF]">
          <Icon size={20} />
        </div>
        <h2 className="text-lg font-semibold text-slate-900">{title}</h2>
      </div>

      <ul className="space-y-2 text-sm text-slate-600 list-disc pl-5">
        {content.map((c, i) => (
          <li key={i}>{c}</li>
        ))}
      </ul>
    </div>
  );
}

export default CardItem;
