import { Send } from "lucide-react";

function Form() {
  return (
    <form className="mt-6 space-y-4" noValidate>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <label className="text-xs text-slate-600">
          Full Name *
          <input
            type="text"
            placeholder="John Doe"
            className="mt-2 w-full rounded-md border border-slate-200 px-3 py-2 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-[#EAE8FF]"
          />
        </label>

        <label className="text-xs text-slate-600">
          Email Address *
          <input
            type="email"
            placeholder="<EMAIL>"
            className="mt-2 w-full rounded-md border border-slate-200 px-3 py-2 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-[#EAE8FF]"
          />
        </label>
      </div>

      <label className="text-xs text-slate-600 block">
        Company
        <input
          type="text"
          placeholder="Your Company Name"
          className="mt-2 w-full rounded-md border border-slate-200 px-3 py-2 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-[#EAE8FF]"
        />
      </label>

      <label className="text-xs text-slate-600 block">
        Subject *
        <input
          type="text"
          placeholder="What can we help you with?"
          className="mt-2 w-full rounded-md border border-slate-200 px-3 py-2 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-[#EAE8FF]"
        />
      </label>

      <label className="text-xs text-slate-600 block">
        Message *
        <textarea
          placeholder="Tell us more about your project and requirements..."
          rows={5}
          className="mt-2 w-full rounded-md border border-slate-200 px-3 py-2 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-[#EAE8FF] resize-none"
        />
      </label>

      <div className="flex items-center justify-between">
        <div className="text-sm text-transparent min-h-[1.2rem]">
          placeholder
        </div>

        <button
          type="button"
          className="inline-flex items-center gap-2 px-5 py-3 rounded-lg bg-white text-black font-medium shadow-md hover:opacity-95"
        >
          <Send size={14} /> <span>Send Message</span>
        </button>
      </div>
    </form>
  );
}

export default Form;
