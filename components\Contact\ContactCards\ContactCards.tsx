import React from "react";
import { CARDS } from "../constants";
import CardItem from "./CardItem";

export default function ContactCards() {
  return (
    <section className="py-12 px-6 bg-white">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 items-stretch">
          {CARDS.map((c) => (
            <CardItem key={c.id} c={c} />
          ))}
        </div>
      </div>
    </section>
  );
}
