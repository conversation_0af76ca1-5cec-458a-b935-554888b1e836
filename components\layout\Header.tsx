"use client";

import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import kodaze<PERSON>ogo from "@/assets/Layout/kodaze-logo.png";

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center gap-2 font-bold text-xl">
            <Image src={kodazeLogo} alt="Kodaze Logo" width={150} height={150} />
          </Link>

          {/* Desktop Menu */}
          <div className="hidden md:flex items-center gap-8">
            <Link href="/" className="text-sm font-medium text-black">
              Home
            </Link>
            <Link href="/services" className="text-sm font-medium text-black">
              Services
            </Link>
            <Link href="/about" className="text-sm font-medium text-black">
              About
            </Link>
            <Link href="/community" className="text-sm font-medium text-black">
              Community
            </Link>
            <Link href="/contact" className="text-sm font-medium text-black">
              Contact
            </Link>
            <a
              href="https://linkedin.com/company/kodaze"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center px-3 h-9 rounded-md text-sm font-semibold text-black shadow hover:scale-105 transform transition"
            >
              Join Community
            </a>
          </div>

          {/* Mobile Hamburger */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="h-10 w-10 flex items-center justify-center rounded-lg hover:bg-gray-100"
            >
              {isOpen ? "OP" : "CLS"}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Dropdown */}
      {isOpen && (
        <div className="md:hidden px-4 pb-4 space-y-4 bg-white shadow">
          <Link href="/" className="block text-sm text-black">
            Home
          </Link>
          <Link href="/services" className="block text-sm text-black">
            Services
          </Link>
          <Link href="/about" className="block text-sm text-black">
            About
          </Link>
          <Link href="/community" className="block text-sm text-black">
            Community
          </Link>
          <Link href="/contact" className="block text-sm text-black">
            Contact
          </Link>
          <a
            href="https://linkedin.com/company/kodaze"
            target="_blank"
            rel="noopener noreferrer"
            className="block w-full text-center px-4 py-2 rounded-md text-sm font-semibold text-black"
          >
            Join Community
          </a>
        </div>
      )}
    </nav>
  );
}