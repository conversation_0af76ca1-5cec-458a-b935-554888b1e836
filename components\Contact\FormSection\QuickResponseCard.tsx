import { MessageCircle } from "lucide-react";

function QuickResponseCard() {
  return (
    <div className="bg-white rounded-xl border border-slate-100 p-6 shadow-sm">
      <div className="flex items-start gap-4">
        <div className="min-w-[40px] h-10 rounded-lg flex items-center justify-center bg-[#F3F1FF]">
          <MessageCircle size={18} />
        </div>
        <div>
          <h4 className="text-sm font-semibold text-slate-900">
            Quick Response
          </h4>
          <p className="mt-2 text-sm text-slate-500">
            Our team typically responds to inquiries within 2–4 hours during
            business hours. For urgent matters, please call us directly.
          </p>

          <ul className="mt-3 text-sm text-slate-500 list-disc ml-5 space-y-1">
            <li>Initial response within 24 hours</li>
            <li>Detailed consultation call scheduled</li>
            <li>Custom solution proposal</li>
            <li>Implementation timeline</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

export default QuickResponseCard;
