import { Mail, Phone, MapPin, Clock } from "lucide-react";
import { Card } from "./contact-types";

export const CARDS: Card[] = [
  {
    id: "email",
    title: "Email Us",
    subtitle: "Get in touch with our team",
    cta: { label: "<EMAIL>", href: "mailto:<EMAIL>" },
    Icon: Mail,
  },
  {
    id: "call",
    title: "Call Us",
    subtitle: "Speak directly with our experts",
    cta: { label: "+****************", href: "tel:+***********" },
    Icon: Phone,
  },
  {
    id: "visit",
    title: "Visit Us",
    subtitle: "Our headquarters",
    cta: { label: "San Francisco, CA", href: "https://maps.google.com?q=San+Francisco+CA" },
    Icon: MapPin,
  },
  {
    id: "hours",
    title: "Business Hours",
    subtitle: "We're available",
    cta: { label: "Mon–Fri: 9AM–6PM PST" },
    Icon: Clock,
  },
];