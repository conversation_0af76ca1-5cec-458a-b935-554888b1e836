// components/ValuesSection.tsx
import React from "react";
import ValueItem from "./ValueItem";
import { VALUES } from "../constants";

export default function ValuesSection() {
  return (
    <section className="py-16 px-6">
      <div className="max-w-6xl mx-auto text-center">
        <h2 className="text-2xl md:text-3xl font-extrabold text-slate-900">
          Our Values
        </h2>
        <p className="mt-3 text-sm md:text-base text-slate-500 max-w-2xl mx-auto">
          The principles that guide everything we do and shape how we build AI
          solutions.
        </p>

        {/* Grid: 1 column on mobile, 2 on sm, 4 on lg */}
        <div className="mt-10 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {VALUES.map((v) => (
            <ValueItem key={v.id} v={v} />
          ))}
        </div>
      </div>
    </section>
  );
}
