import type { ValueItemType } from "../aboutSection-types.js";

function ValueItem({ v }: { v: ValueItemType }) {
  return (
    <article
      className="bg-white rounded-xl border border-slate-100 p-6 text-center shadow-sm hover:shadow-md transition"
      aria-labelledby={`value-${v.id}-title`}
    >
      <div className="mx-auto w-14 h-14 rounded-lg flex items-center justify-center bg-[#F3F1FF] mb-4">
        <v.Icon size={22} />
      </div>

      <h3
        id={`value-${v.id}-title`}
        className="text-sm md:text-base font-semibold text-slate-900 mb-2"
      >
        {v.title}
      </h3>

      <p className="text-sm text-slate-500 leading-relaxed">{v.description}</p>
    </article>
  );
}

export default ValueItem;
