import { Search } from "lucide-react";

function SearchSection() {
  return (
    <section className="py-8 border-b border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
          <div className="relative w-full lg:w-96">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              placeholder="Search articles, case studies, tools..."
              className="pl-10"
            />
          </div>
        </div>
      </div>
    </section>
  );
}

export default SearchSection;
