import { CARDS } from "../constants";
import CardItem from "./CardItem";

export default function PrivacyCards() {
  return (
    <section className="py-16 px-6">
      <div className="max-w-4xl mx-auto">
        <div className="mt-12 space-y-6">
          {CARDS.map(({ id, icon: Icon, title, content }) => (
            <CardItem key={id} id={id} icon={Icon} title={title} content={content} />
          ))}
        </div>
      </div>
    </section>
  );
}
