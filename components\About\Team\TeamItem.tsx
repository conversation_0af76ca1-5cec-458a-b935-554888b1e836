import { Team<PERSON>ember } from "../aboutSection-types";

function initials(name: string) {
  const parts = name.trim().split(/\s+/);
  if (parts.length === 1) return parts[0].slice(0, 2).toUpperCase();
  return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
}

function TeamItem({ member }: { member: TeamMember }) {
  return (
    <article
      key={member.id}
      className="bg-white rounded-xl border border-slate-100 p-6 text-center shadow-sm hover:shadow-md transition focus-within:ring-2 focus-within:ring-[#EAE8FF] outline-none"
      tabIndex={0}
      aria-labelledby={`team-${member.id}-name`}
    >
      <div className="mx-auto w-20 h-20 rounded-full flex items-center justify-center mb-4 bg-black">
        <span className="text-white font-bold text-lg">
          {initials(member.name)}
        </span>
      </div>

      <h3
        id={`team-${member.id}-name`}
        className="text-lg font-semibold text-slate-900"
      >
        {member.name}
      </h3>

      <div className="text-sm mt-1">{member.role}</div>

      <p className="mt-4 text-sm text-slate-500 leading-relaxed min-h-[64px]">
        {member.description}
      </p>

      <div className="mt-4 w-full text-left">
        <div className="text-xs font-semibold text-slate-500 mb-2">
          EXPERTISE
        </div>

        <div className="flex flex-wrap gap-2">
          {member.expertise.map((tag) => (
            <span
              key={tag}
              className="text-xs px-2.5 py-1 rounded-full bg-[#F3F1FF] border border-transparent"
            >
              {tag}
            </span>
          ))}
        </div>
      </div>
    </article>
  );
}

export default TeamItem;
