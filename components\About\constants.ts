import { <PERSON>, Heart, Target, Users } from "lucide-react";
import { TeamMember, ValueItemType } from "./aboutSection-types";

export const VALUES: ValueItemType[] = [
  {
    id: "innovation",
    title: "Innovation First",
    description:
      "We constantly push the boundaries of what's possible with AI technology.",
    Icon: Brain,
  },
  {
    id: "customer",
    title: "Customer-Centric",
    description:
      "Every solution we build is designed with our clients' success in mind.",
    Icon: Users,
  },
  {
    id: "results",
    title: "Results Driven",
    description:
      "We measure our success by the tangible impact we create for businesses.",
    Icon: Target,
  },
  {
    id: "ethical",
    title: "Ethical AI",
    description:
      "We believe in responsible AI development that benefits everyone.",
    Icon: Heart,
  },
];

export const TEAM: TeamMember[] = [
  {
    id: "alex",
    name: "<PERSON>",
    role: "CEO & Co-Founder",
    description:
      "Former AI researcher at Google with 10+ years in machine learning and business automation.",
    expertise: ["AI Strategy", "Machine Learning", "Business Development"],
  },
  {
    id: "sarah",
    name: "<PERSON>",
    role: "<PERSON>O & Co-Founder",
    description:
      "Ex-Microsoft engineer specializing in scalable AI systems and marketing technology platforms.",
    expertise: [
      "System Architecture",
      "AI Engineering",
      "Technical Leadership",
    ],
  },
  {
    id: "micha<PERSON>",
    name: "<PERSON>",
    role: "Head of Marketing AI",
    description:
      "Marketing technology veteran focused on customer analytics and campaign optimization.",
    expertise: ["Marketing Analytics", "Customer Insights", "Growth Strategy"],
  },
  {
    id: "emily",
    name: "Emily Watson",
    role: "Head of Business Solutions",
    description:
      "Business process optimization expert helping enterprises transform through intelligent automation.",
    expertise: [
      "Process Automation",
      "Enterprise Solutions",
      "Change Management",
    ],
  },
];