import { ExternalLink, Linkedin, MessageSquare } from "lucide-react";

function CommunityHero() {
  return (
    <section className="pt-40 pb-16 bg-gradient-subtle">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-6 animate-fade-in">
          <h1 className="text-4xl md:text-5xl font-bold">
            AI <span className="text-gradient">Community & Resources</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Stay updated with the latest AI trends, case studies, and insights.
            Join our community of forward-thinking business leaders and AI
            enthusiasts.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="https://linkedin.com/company/kodaze"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center px-3 h-9 rounded-md text-sm font-semibold text-black shadow hover:scale-105 transform transition"
            >
              <Linkedin className="h-4 w-4 mr-2" />
              Join LinkedIn Community
              <ExternalLink className="h-4 w-4 ml-2" />
            </a>
            <button className="flex items-center justify-center px-3 h-9 rounded-md text-sm font-semibold text-black shadow hover:scale-105 transform transition">
              <MessageSquare className="h-4 w-4 mr-2" />
              Subscribe to Updates
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}

export default CommunityHero;
