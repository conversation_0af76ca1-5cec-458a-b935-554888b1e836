import {
  Eye,
  FileText,
  Users,
  Shield,
  Database,
  Globe,
  RefreshCw,
} from "lucide-react";
import { PrivacyCard } from "./privacy-types";

export const CARDS: PrivacyCard[] = [
  {
    id: "info-collect",
    icon: Eye,
    title: "Information We Collect",
    content: [
      "Personal Information: Name, email address, phone number, and any other details you voluntarily provide when contacting us, subscribing to newsletters, or filling out forms.",
      "Usage Data: Information about how you interact with our Services, such as IP address, browser type, operating system, referring URLs, and pages visited.",
      "Cookies & Tracking: We use cookies and similar technologies to improve user experience and analyze traffic. You may disable cookies through your browser settings.",
      "Third-Party Content & APIs: If you interact with content from third-party platforms (e.g., LinkedIn, Twitter, GitHub), those platforms may collect data in accordance with their own privacy policies.",
    ],
  },
  {
    id: "info-use",
    icon: FileText,
    title: "How We Use Information",
    content: [
      "Provide, maintain, and improve our Services.",
      "Share company news, blog posts, and product updates.",
      "Understand website traffic and user behavior.",
      "Integrate content from third-party services such as LinkedIn company posts.",
      "Comply with legal obligations or protect against misuse of our Services.",
    ],
  },
  {
    id: "third-party",
    icon: Users,
    title: "Third-Party Integrations",
    content: [
      "Our Services may display or embed content from third-party platforms such as LinkedIn.",
      "When we publish LinkedIn company posts on our blog, we may retrieve content via LinkedIn’s public embedding tools or authorized APIs.",
      "We do not collect or store your personal LinkedIn data unless you explicitly interact with our Services (e.g., contact forms).",
      "Your interactions with third-party services are governed by those platforms’ privacy policies.",
    ],
  },
  {
    id: "data-sharing",
    icon: Users,
    title: "Data Sharing",
    content: [
      "We do not sell or rent your personal information.",
      "We may share information only in these cases:",
      "• With service providers who help us operate the Services (e.g., analytics, hosting).",
      "• To comply with legal obligations or enforce our rights.",
      "• During business transfers, such as a merger, acquisition, or sale of assets.",
    ],
  },
  {
    id: "data-security",
    icon: Shield,
    title: "Data Security",
    content: [
      "We take reasonable technical and organizational measures to protect your data against unauthorized access, alteration, disclosure, or destruction.",
      "However, no method of transmission or storage is 100% secure.",
    ],
  },
  {
    id: "user-rights",
    icon: FileText,
    title: "Your Rights",
    content: [
      "Accessing, correcting, or deleting your personal information.",
      "Opting out of certain data processing (e.g., marketing emails).",
      "Withdrawing consent for processing where applicable.",
      "To exercise your rights, contact <NAME_EMAIL>.",
    ],
  },
  {
    id: "data-retention",
    icon: Database,
    title: "Data Retention",
    content: [
      "We retain personal information only for as long as necessary to fulfill the purposes described in this Policy, unless a longer retention period is required by law.",
    ],
  },
  {
    id: "international",
    icon: Globe,
    title: "International Transfers",
    content: [
      "If you access our Services outside your country, your data may be transferred and processed in countries with different data protection laws.",
    ],
  },
  {
    id: "updates",
    icon: RefreshCw,
    title: "Updates to This Policy",
    content: [
      "We may update this Privacy Policy from time to time.",
      "The updated version will be indicated by the “Last Updated” date and posted on this page.",
    ],
  },
];
